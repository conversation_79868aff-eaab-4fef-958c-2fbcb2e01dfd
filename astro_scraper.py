#!/usr/bin/env python3
"""
Astro-Databank Web Scraper
Scrapes astrological profiles from astro.com/astro-databank
Features: Tor integration, proxy rotation, comprehensive data extraction
"""

import os
import time
import random
import pandas as pd
import requests
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from stem import Signal
from stem.control import Controller

# Configuration
TOR_PASSWORD = 'mytorpassword'  # Change this to your actual Tor password
USE_TOR = True  # Set to False to disable Tor and use only proxies
MAX_RETRIES = 3
DELAY_RANGE = (2, 5)  # Random delay between requests
BATCH_SIZE = 50  # Save data every N profiles

class AstroScraper:
    def __init__(self):
        self.data = []
        self.failed_urls = []
        
    def rotate_tor_ip(self):
        """Rotate Tor IP address"""
        try:
            with Controller.from_port(port=9051) as controller:
                controller.authenticate(password=TOR_PASSWORD)
                controller.signal(Signal.NEWNYM)
                print("🔄 Tor IP rotated.")
                time.sleep(5)  # Wait for new circuit
        except Exception as e:
            print(f"[Tor Error] IP rotation failed: {e}")

    def fetch_free_proxies(self):
        """Fetch fresh proxies from free-proxy-list.net"""
        try:
            url = "https://free-proxy-list.net/"
            response = requests.get(url, timeout=10)
            soup = BeautifulSoup(response.text, "html.parser")

            proxies = []
            for row in soup.select("table tbody tr"):
                cols = row.find_all("td")
                if len(cols) >= 7:
                    ip = cols[0].text.strip()
                    port = cols[1].text.strip()
                    https = cols[6].text.strip()
                    if https == "yes":
                        proxies.append(f"{ip}:{port}")
            
            print(f"🔗 Fetched {len(proxies)} fresh proxies")
            return proxies
        except Exception as e:
            print(f"[Error] Failed to fetch proxies: {e}")
            return []

    def get_proxies(self):
        """Load proxies from file or fetch fresh ones"""
        try:
            with open("proxies.txt", "r") as f:
                proxies = [line.strip() for line in f if line.strip()]
            print(f"📁 Loaded {len(proxies)} proxies from file")
            return proxies
        except FileNotFoundError:
            print("📁 Proxy file not found, fetching fresh proxies...")
            return self.fetch_free_proxies()

    def get_random_proxy(self):
        """Get a random proxy (including Tor if enabled)"""
        proxies = self.get_proxies()
        if USE_TOR:
            proxies.append("127.0.0.1:9050")
        return random.choice(proxies) if proxies else None

    def create_driver(self, proxy=None):
        """Create Selenium WebDriver with proxy and user-agent"""
        ua = UserAgent()
        user_agent = ua.random
        options = Options()
        
        # Chrome options for stealth
        options.add_argument("--incognito")
        options.add_argument("--headless=new")
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--no-sandbox")
        options.add_argument("--disable-dev-shm-usage")
        options.add_argument("--disable-gpu")
        options.add_argument("--window-size=1920,1080")
        options.add_argument(f"user-agent={user_agent}")
        
        # Proxy configuration
        if proxy:
            if proxy.startswith("127.0.0.1:9050"):
                options.add_argument("--proxy-server=socks5://127.0.0.1:9050")
                print("🧅 Using Tor proxy")
            else:
                options.add_argument(f"--proxy-server=http://{proxy}")
                print(f"🛡 Using proxy: {proxy}")
        else:
            print("⚠ No proxy used.")

        try:
            driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()), 
                options=options
            )
            driver.set_page_load_timeout(30)
            return driver
        except Exception as e:
            print(f"[Error] Failed to create driver: {e}")
            return None

    def get_all_links(self, driver):
        """Get all Astro-Databank profile URLs"""
        try:
            driver.get("https://www.astro.com/astro-databank/Special:AllPages")
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, ".mw-allpages-body"))
            )

            links = set()
            page_count = 0
            
            while True:
                page_count += 1
                print(f"📄 Processing page {page_count}")
                
                page_links = driver.find_elements(By.CSS_SELECTOR, ".mw-allpages-body a")
                for link in page_links:
                    href = link.get_attribute("href")
                    if href and href.startswith("https://www.astro.com/astro-databank/"):
                        links.add(href)

                try:
                    next_link = WebDriverWait(driver, 5).until(
                        EC.element_to_be_clickable((By.LINK_TEXT, "Next page"))
                    )
                    next_link.click()
                    time.sleep(random.uniform(2, 4))
                except:
                    print("📄 No more pages found")
                    break

            print(f"🔗 Total profiles found: {len(links)}")
            return list(links)
        except Exception as e:
            print(f"[Error] Failed to get links: {e}")
            return []

    def scrape_profile(self, driver, url):
        """Scrape a single profile"""
        try:
            driver.get(url)
            WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "h1.firstHeading"))
            )

            # Extract name
            try:
                name = driver.find_element(By.CSS_SELECTOR, "h1.firstHeading").text.strip()
            except:
                name = None

            birth_data = rodden = biography = chart_image = None

            # Extract birth data and Rodden rating from infobox
            try:
                info_box = driver.find_element(By.CLASS_NAME, "infobox")
                rows = info_box.find_elements(By.TAG_NAME, "tr")
                for row in rows:
                    text = row.text
                    if "Birth" in text:
                        birth_data = text.strip()
                    elif "Rodden Rating" in text:
                        rodden = text.strip()
            except:
                pass

            # Extract full biography
            try:
                content = driver.find_element(By.CLASS_NAME, "mw-parser-output")
                biography = content.text.strip()
            except:
                biography = None

            # Extract chart image
            try:
                img = driver.find_element(By.CSS_SELECTOR, ".mw-parser-output img")
                chart_image = img.get_attribute("src")
            except:
                chart_image = None

            return {
                "Name": name,
                "Birth Data": birth_data,
                "Rodden Rating": rodden,
                "Biography": biography,
                "Chart Image": chart_image,
                "URL": url
            }
        except Exception as e:
            print(f"[Error] Failed to scrape {url}: {e}")
            return None

    def save_data(self, filename="astrodatabank_profiles.csv"):
        """Save scraped data to CSV"""
        if self.data:
            df = pd.DataFrame(self.data)
            df.to_csv(filename, index=False, encoding="utf-8")
            print(f"💾 Saved {len(self.data)} profiles to {filename}")

    def run(self):
        """Main scraping loop"""
        print("🚀 Starting Astro-Databank scraper...")
        
        # Initialize driver
        proxy = self.get_random_proxy()
        if proxy == "127.0.0.1:9050":
            self.rotate_tor_ip()

        driver = self.create_driver(proxy)
        if not driver:
            print("❌ Failed to create driver. Exiting.")
            return

        try:
            # Get all profile links
            links = self.get_all_links(driver)
            if not links:
                print("❌ No links found. Exiting.")
                return

            # Scrape profiles
            for idx, url in enumerate(links):
                print(f"[{idx+1}/{len(links)}] Scraping: {url}")
                
                retries = 0
                while retries < MAX_RETRIES:
                    try:
                        result = self.scrape_profile(driver, url)
                        if result:
                            self.data.append(result)
                            break
                        else:
                            retries += 1
                    except Exception as e:
                        print(f"[Error] Attempt {retries+1} failed: {e}")
                        retries += 1
                        time.sleep(2)

                if retries == MAX_RETRIES:
                    self.failed_urls.append(url)
                    print(f"❌ Failed to scrape {url} after {MAX_RETRIES} attempts")

                # Save data periodically
                if (idx + 1) % BATCH_SIZE == 0:
                    self.save_data()

                # Rotate IP every 10 requests if using Tor
                if USE_TOR and (idx + 1) % 10 == 0:
                    self.rotate_tor_ip()
                    driver.quit()
                    proxy = self.get_random_proxy()
                    driver = self.create_driver(proxy)

                # Random delay
                time.sleep(random.uniform(*DELAY_RANGE))

        finally:
            driver.quit()
            
        # Final save
        self.save_data()
        
        # Report results
        print(f"\n✅ Scraping completed!")
        print(f"📊 Successfully scraped: {len(self.data)} profiles")
        print(f"❌ Failed to scrape: {len(self.failed_urls)} profiles")
        
        if self.failed_urls:
            with open("failed_urls.txt", "w") as f:
                for url in self.failed_urls:
                    f.write(url + "\n")
            print("📝 Failed URLs saved to failed_urls.txt")

if __name__ == "__main__":
    scraper = AstroScraper()
    scraper.run()
