#!/usr/bin/env python3
"""
Test script for Astro-Databank Web Scraper
Tests basic functionality and configuration
"""

import sys
import time
from astro_scraper import AstroScraper

def test_proxy_fetching():
    """Test proxy fetching functionality"""
    print("🧪 Testing proxy fetching...")
    scraper = AstroScraper()
    
    try:
        proxies = scraper.fetch_free_proxies()
        if proxies:
            print(f"✅ Successfully fetched {len(proxies)} proxies")
            return True
        else:
            print("❌ No proxies fetched")
            return False
    except Exception as e:
        print(f"❌ Proxy fetching failed: {e}")
        return False

def test_driver_creation():
    """Test WebDriver creation"""
    print("🧪 Testing WebDriver creation...")
    scraper = AstroScraper()
    
    try:
        # Test without proxy
        driver = scraper.create_driver()
        if driver:
            print("✅ Driver created successfully (no proxy)")
            driver.quit()
            
            # Test with proxy
            proxy = scraper.get_random_proxy()
            if proxy:
                driver = scraper.create_driver(proxy)
                if driver:
                    print(f"✅ Driver created successfully with proxy: {proxy}")
                    driver.quit()
                    return True
                else:
                    print("❌ Failed to create driver with proxy")
                    return False
            else:
                print("⚠️ No proxy available for testing")
                return True
        else:
            print("❌ Failed to create driver")
            return False
    except Exception as e:
        print(f"❌ Driver creation failed: {e}")
        return False

def test_single_profile():
    """Test scraping a single profile"""
    print("🧪 Testing single profile scraping...")
    scraper = AstroScraper()
    
    # Test URL - a well-known profile
    test_url = "https://www.astro.com/astro-databank/Einstein,_Albert"
    
    try:
        driver = scraper.create_driver()
        if not driver:
            print("❌ Failed to create driver for testing")
            return False
        
        print(f"🔍 Testing with URL: {test_url}")
        result = scraper.scrape_profile(driver, test_url)
        driver.quit()
        
        if result and result.get('Name'):
            print(f"✅ Successfully scraped profile: {result['Name']}")
            print(f"   Birth Data: {result.get('Birth Data', 'N/A')}")
            print(f"   Rodden Rating: {result.get('Rodden Rating', 'N/A')}")
            return True
        else:
            print("❌ Failed to scrape profile data")
            return False
    except Exception as e:
        print(f"❌ Profile scraping failed: {e}")
        return False

def test_tor_connection():
    """Test Tor connection (if enabled)"""
    print("🧪 Testing Tor connection...")
    scraper = AstroScraper()
    
    try:
        scraper.rotate_tor_ip()
        print("✅ Tor IP rotation successful")
        return True
    except Exception as e:
        print(f"❌ Tor connection failed: {e}")
        print("💡 This is normal if Tor is not installed or configured")
        return False

def run_all_tests():
    """Run all tests"""
    print("🚀 Running Astro-Databank Scraper Tests")
    print("=" * 50)
    
    tests = [
        ("Proxy Fetching", test_proxy_fetching),
        ("WebDriver Creation", test_driver_creation),
        ("Single Profile Scraping", test_single_profile),
        ("Tor Connection", test_tor_connection)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        start_time = time.time()
        success = test_func()
        duration = time.time() - start_time
        
        results.append((test_name, success, duration))
        print(f"⏱️ Duration: {duration:.2f}s")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, success, duration in results:
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name} ({duration:.2f}s)")
        if success:
            passed += 1
    
    print(f"\n🎯 Tests passed: {passed}/{len(tests)}")
    
    if passed == len(tests):
        print("🎉 All tests passed! Your scraper is ready to use.")
        print("\n🚀 Run the scraper with: python astro_scraper.py")
    else:
        print("⚠️ Some tests failed. Check the configuration and dependencies.")
        print("\n💡 Tips:")
        print("- Ensure all dependencies are installed: pip install -r requirements.txt")
        print("- Check your internet connection")
        print("- For Tor tests, ensure Tor is installed and configured")

if __name__ == "__main__":
    if len(sys.argv) > 1:
        test_name = sys.argv[1].lower()
        if test_name == "proxy":
            test_proxy_fetching()
        elif test_name == "driver":
            test_driver_creation()
        elif test_name == "profile":
            test_single_profile()
        elif test_name == "tor":
            test_tor_connection()
        else:
            print("Available tests: proxy, driver, profile, tor")
    else:
        run_all_tests()
