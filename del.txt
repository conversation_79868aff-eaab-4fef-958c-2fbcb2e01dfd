import requests
from bs4 import BeautifulSoup

def fetch_free_proxies():
    url = "https://free-proxy-list.net/"
    response = requests.get(url)
    soup = BeautifulSoup(response.text, "html.parser")

    proxies = []
    for row in soup.select("table tbody tr"):
        cols = row.find_all("td")
        ip = cols[0].text.strip()
        port = cols[1].text.strip()
        https = cols[6].text.strip()
        if https == "yes":
            proxies.append(f"{ip}:{port}")
    return proxies

def save_proxies_to_file(proxies, filename="proxies.txt"):
    with open(filename, "w") as f:
        for proxy in proxies:
            f.write(proxy + "\n")

if __name__ == "__main__":
    proxies = fetch_free_proxies()
    save_proxies_to_file(proxies)
    print(f"✅ Saved {len(proxies)} proxies to proxies.txt")