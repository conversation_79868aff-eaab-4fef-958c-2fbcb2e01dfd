# Astro-Databank Web Scraper

A comprehensive web scraper for extracting astrological profiles from [astro.com/astro-databank](https://www.astro.com/astro-databank). Features Tor integration, proxy rotation, and robust error handling.

## Features

- 🧅 **Tor Integration**: Automatic IP rotation through Tor network
- 🛡️ **Proxy Support**: HTTP/HTTPS proxy rotation with fresh proxy fetching
- 🎭 **Stealth Mode**: Random user agents and headless browsing
- 📊 **Comprehensive Data**: Extracts names, birth data, Rodden ratings, biographies, and chart images
- 💾 **Batch Saving**: Periodic data saves to prevent loss
- 🔄 **Error Recovery**: Retry mechanisms and failed URL tracking
- ⚡ **Performance**: Optimized delays and batch processing

## Installation

### Quick Setup

1. **Clone/Download** this repository
2. **Run setup script**:
   ```bash
   python setup.py
   ```

### Manual Setup

1. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Tor** (optional but recommended):
   - **Windows**: Download [Tor Browser](https://www.torproject.org/)
   - **Linux**: `sudo apt-get install tor` (Ubuntu/Debian)
   - **macOS**: `brew install tor`

3. **Configure Tor** (if using):
   - Add to `/etc/tor/torrc` (Linux/macOS) or Tor Browser's torrc:
   ```
   ControlPort 9051
   HashedControlPassword 16:872860B76453A77D60CA2BB8C1A7042072093276A3D701AD684053EC4C
   SocksPort 9050
   ```
   - Restart Tor service: `sudo systemctl restart tor`

## Configuration

### Basic Configuration

Edit `astro_scraper.py`:

```python
# Tor Configuration
TOR_PASSWORD = 'mytorpassword'  # Change to your Tor password
USE_TOR = True  # Set to False to disable Tor

# Scraping Configuration
MAX_RETRIES = 3
DELAY_RANGE = (2, 5)  # Random delay between requests (seconds)
BATCH_SIZE = 50  # Save data every N profiles
```

### Tor Password Setup

The default password is `mytorpassword`. To generate a new one:

```bash
tor --hash-password "your_new_password"
```

Update both the `TOR_PASSWORD` in the script and the `HashedControlPassword` in torrc.

## Usage

### Basic Usage

```bash
python astro_scraper.py
```

### Advanced Usage

```python
from astro_scraper import AstroScraper

# Create scraper instance
scraper = AstroScraper()

# Run scraping
scraper.run()

# Access scraped data
print(f"Scraped {len(scraper.data)} profiles")
```

## Output

### Data Files

- **`astrodatabank_profiles.csv`**: Main output with all scraped profiles
- **`failed_urls.txt`**: URLs that failed to scrape (for retry)
- **`proxies.txt`**: Current proxy list

### Data Structure

Each profile contains:
- **Name**: Person's name
- **Birth Data**: Birth date, time, and location
- **Rodden Rating**: Data reliability rating (AA, A, B, C, DD, X)
- **Biography**: Full biographical text
- **Chart Image**: URL to astrological chart image
- **URL**: Original profile URL

## Proxy Management

### Automatic Proxy Fetching

The scraper automatically fetches fresh proxies from free-proxy-list.net:

```python
# Update proxies manually
python del.txt
```

### Custom Proxy List

Add your own proxies to `proxies.txt`:
```
proxy1.example.com:8080
proxy2.example.com:3128
```

## Performance & Ethics

### Rate Limiting

- Random delays between requests (2-5 seconds)
- IP rotation every 10 requests (when using Tor)
- Batch processing to minimize server load

### Best Practices

- ✅ Use VPN for additional anonymity
- ✅ Monitor your IP rotation
- ✅ Respect robots.txt and terms of service
- ✅ Run during off-peak hours
- ❌ Don't overwhelm the server
- ❌ Don't scrape for commercial purposes without permission

## Troubleshooting

### Common Issues

**1. Tor Connection Failed**
```
[Tor Error] IP rotation failed: [Errno 111] Connection refused
```
- Solution: Ensure Tor is running and ControlPort is configured

**2. Proxy Errors**
```
[Error] Failed to create driver: Message: unknown error: net::ERR_PROXY_CONNECTION_FAILED
```
- Solution: Update proxy list or disable proxy usage

**3. Chrome Driver Issues**
```
[Error] Failed to create driver: Message: 'chromedriver' executable needs to be in PATH
```
- Solution: The script auto-downloads ChromeDriver, ensure internet connection

**4. Memory Issues**
```
MemoryError: Unable to allocate array
```
- Solution: Reduce BATCH_SIZE or increase system memory

### Debug Mode

Enable verbose logging:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## Legal & Ethical Considerations

- This tool is for educational and research purposes only
- Respect the website's terms of service and robots.txt
- Consider the server load and use appropriate delays
- Don't use scraped data for commercial purposes without permission
- Be aware of copyright and data protection laws in your jurisdiction

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is for educational purposes. Please respect the terms of service of the websites you scrape.

## Support

For issues and questions:
1. Check the troubleshooting section
2. Review the configuration settings
3. Test with a small subset of URLs first
4. Ensure all dependencies are properly installed

---

**Disclaimer**: This tool is provided as-is for educational purposes. Users are responsible for complying with applicable laws and website terms of service.
