import os
import time
import random
import pandas as pd
from fake_useragent import UserAgent
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from stem import Signal
from stem.control import Controller

# 👇 Your plaintext Tor password (not the hash)
TOR_PASSWORD = 'mytorpassword'  # Change this to your actual password
USE_TOR = True  # Set to False to disable <PERSON> and use only proxies

# 🔁 Rotate Tor IP
def rotate_tor_ip():
    try:
        with Controller.from_port(port=9051) as controller:
            controller.authenticate(password=TOR_PASSWORD)
            controller.signal(Signal.NEWNYM)
            print("🔄 Tor IP rotated.")
    except Exception as e:
        print(f"[Tor Error] IP rotation failed: {e}")

# 📁 Load proxies from file
def get_proxies():
    try:
        with open("proxies.txt", "r") as f:
            proxies = [line.strip() for line in f if line.strip()]
        return proxies
    except FileNotFoundError:
        return []

# 🎲 Random proxy (including Tor if enabled)
def get_random_proxy():
    proxies = get_proxies()
    if USE_TOR:
        proxies.append("127.0.0.1:9050")
    return random.choice(proxies) if proxies else None

# 🧪 Setup Selenium with proxy & user-agent
def create_driver(proxy=None):
    ua = UserAgent()
    user_agent = ua.random
    options = Options()
    options.add_argument("--incognito")
    options.add_argument("--headless=new")
    options.add_argument("--disable-blink-features=AutomationControlled")
    options.add_argument("--no-sandbox")
    options.add_argument("--disable-dev-shm-usage")
    options.add_argument(f"user-agent={user_agent}")

    if proxy:
        if proxy.startswith("127.0.0.1:9050"):
            options.add_argument("--proxy-server=socks5://127.0.0.1:9050")
            print("🧅 Using Tor proxy")
        else:
            options.add_argument(f"--proxy-server=http://{proxy}")
            print(f"🛡 Using proxy: {proxy}")
    else:
        print("⚠ No proxy used.")

    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
    return driver

# 🔗 Get all Astro-Databank profile URLs
def get_all_links(driver):
    driver.get("https://www.astro.com/astro-databank/Special:AllPages")
    time.sleep(3)

    links = set()
    while True:
        page_links = driver.find_elements(By.CSS_SELECTOR, ".mw-allpages-body a")
        for link in page_links:
            href = link.get_attribute("href")
            if href and href.startswith("https://www.astro.com/astro-databank/"):
                links.add(href)

        try:
            next_link = driver.find_element(By.LINK_TEXT, "Next page")
            next_link.click()
            time.sleep(random.uniform(2, 4))
        except:
            break

    print(f"🔗 Total profiles found: {len(links)}")
    return list(links)

# 📄 Scrape a single profile
def scrape_profile(driver, url):
    driver.get(url)
    time.sleep(random.uniform(1.5, 3.5))

    try:
        name = driver.find_element(By.CSS_SELECTOR, "h1.firstHeading").text.strip()
    except:
        name = None

    birth_data = rodden = biography = chart_image = None

    # Birth data and Rodden rating
    try:
        info_box = driver.find_element(By.CLASS_NAME, "infobox")
        rows = info_box.find_elements(By.TAG_NAME, "tr")
        for row in rows:
            text = row.text
            if "Birth" in text:
                birth_data = text.strip()
            elif "Rodden Rating" in text:
                rodden = text.strip()
    except:
        pass

    # Full biography
    try:
        content = driver.find_element(By.CLASS_NAME, "mw-parser-output")
        biography = content.text.strip()
    except:
        biography = None

    # Chart image
    try:
        img = driver.find_element(By.CSS_SELECTOR, ".mw-parser-output img")
        chart_image = img.get_attribute("src")
    except:
        chart_image = None

    return {
        "Name": name,
        "Birth Data": birth_data,
        "Rodden Rating": rodden,
        "Biography": biography,
        "Chart Image": chart_image,
        "URL": url
    }

# 🧠 Main scraping loop
def main():
    proxy = get_random_proxy()
    if proxy == "127.0.0.1:9050":
        rotate_tor_ip()

    driver = create_driver(proxy)
    links = get_all_links(driver)

    data = []
    for idx, url in enumerate(links):
        print(f"[{idx+1}/{len(links)}] Scraping: {url}")
        try:
            result = scrape_profile(driver, url)
            data.append(result)
        except Exception as e:
            print(f"[Error] Failed to scrape {url}: {e}")

        # Rotate every 10 requests if using Tor
        if USE_TOR and (idx + 1) % 10 == 0:
            rotate_tor_ip()
            driver.quit()
            proxy = get_random_proxy()
            driver = create_driver(proxy)

        time.sleep(random.uniform(2, 5))

    driver.quit()
    df = pd.DataFrame(data)
    df.to_csv("astrodatabank_full.csv", index=False, encoding="utf-8")
    print("✅ Saved to astrodatabank_full.csv")

if __name__ == "__main__":
    main()


