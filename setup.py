#!/usr/bin/env python3
"""
Setup script for Astro-Databank Web Scraper
Handles dependency installation and Tor configuration
"""

import os
import sys
import subprocess
import platform

def run_command(command):
    """Run a shell command and return success status"""
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {command}")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {command}")
        print(f"Error: {e.stderr}")
        return False

def install_python_dependencies():
    """Install Python dependencies"""
    print("📦 Installing Python dependencies...")
    return run_command(f"{sys.executable} -m pip install -r requirements.txt")

def check_tor_installation():
    """Check if Tor is installed"""
    print("🧅 Checking Tor installation...")
    
    system = platform.system().lower()
    
    if system == "windows":
        # Check if Tor Browser is installed
        tor_paths = [
            r"C:\Users\<USER>\Desktop\Tor Browser\Browser\TorBrowser\Tor\tor.exe".format(os.getenv('USERNAME')),
            r"C:\Program Files\Tor Browser\Browser\TorBrowser\Tor\tor.exe",
            r"C:\Program Files (x86)\Tor Browser\Browser\TorBrowser\Tor\tor.exe"
        ]
        
        for path in tor_paths:
            if os.path.exists(path):
                print(f"✅ Tor found at: {path}")
                return True
        
        print("❌ Tor not found. Please install Tor Browser from https://www.torproject.org/")
        print("💡 Alternative: Install Tor service using 'choco install tor' (requires Chocolatey)")
        return False
        
    elif system == "linux":
        # Check if tor service is available
        if run_command("which tor"):
            print("✅ Tor is installed")
            return True
        else:
            print("❌ Tor not found. Install with:")
            print("   Ubuntu/Debian: sudo apt-get install tor")
            print("   CentOS/RHEL: sudo yum install tor")
            print("   Arch: sudo pacman -S tor")
            return False
            
    elif system == "darwin":  # macOS
        if run_command("which tor"):
            print("✅ Tor is installed")
            return True
        else:
            print("❌ Tor not found. Install with:")
            print("   Homebrew: brew install tor")
            return False
    
    return False

def setup_tor_config():
    """Setup Tor configuration"""
    print("⚙️ Setting up Tor configuration...")
    
    system = platform.system().lower()
    
    if system == "windows":
        config_path = os.path.expanduser("~\\Desktop\\Tor Browser\\Browser\\TorBrowser\\Data\\Tor\\torrc")
    else:
        config_path = "/etc/tor/torrc"
    
    config_content = """
# Astro-Databank Scraper Configuration
ControlPort 9051
HashedControlPassword 16:872860B76453A77D60CA2BB8C1A7042072093276A3D701AD684053EC4C
SocksPort 9050
"""
    
    try:
        if system == "windows":
            print("📝 For Windows Tor Browser, manually add these lines to torrc:")
        else:
            print(f"📝 Add these lines to {config_path}:")
        
        print(config_content)
        
        if system != "windows":
            print("💡 You may need to run: sudo systemctl restart tor")
        
        return True
    except Exception as e:
        print(f"❌ Failed to setup Tor config: {e}")
        return False

def update_proxies():
    """Update proxy list"""
    print("🔄 Updating proxy list...")
    try:
        import requests
        from bs4 import BeautifulSoup
        
        url = "https://free-proxy-list.net/"
        response = requests.get(url, timeout=10)
        soup = BeautifulSoup(response.text, "html.parser")

        proxies = []
        for row in soup.select("table tbody tr"):
            cols = row.find_all("td")
            if len(cols) >= 7:
                ip = cols[0].text.strip()
                port = cols[1].text.strip()
                https = cols[6].text.strip()
                if https == "yes":
                    proxies.append(f"{ip}:{port}")

        with open("proxies.txt", "w") as f:
            for proxy in proxies:
                f.write(proxy + "\n")
        
        print(f"✅ Updated {len(proxies)} proxies in proxies.txt")
        return True
    except Exception as e:
        print(f"❌ Failed to update proxies: {e}")
        return False

def main():
    """Main setup function"""
    print("🚀 Astro-Databank Web Scraper Setup")
    print("=" * 40)
    
    success = True
    
    # Install Python dependencies
    if not install_python_dependencies():
        success = False
    
    # Check Tor installation
    if not check_tor_installation():
        print("⚠️ Tor not found. You can still use the scraper with proxies only.")
        print("   Set USE_TOR = False in astro_scraper.py")
    
    # Setup Tor configuration
    setup_tor_config()
    
    # Update proxies
    if not update_proxies():
        success = False
    
    print("\n" + "=" * 40)
    if success:
        print("✅ Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure Tor password in astro_scraper.py (TOR_PASSWORD)")
        print("2. Run: python astro_scraper.py")
    else:
        print("⚠️ Setup completed with some issues.")
        print("   Check the error messages above and resolve them.")
    
    print("\n💡 Tips:")
    print("- Use a VPN for additional anonymity")
    print("- Monitor your IP rotation during scraping")
    print("- Respect the website's robots.txt and terms of service")

if __name__ == "__main__":
    main()
